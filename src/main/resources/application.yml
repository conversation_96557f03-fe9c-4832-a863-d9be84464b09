server:
  port: 7071
  servlet:
    encoding:
      charset: UTF-8
      force: true
      enabled: true

spring:
  application:
    name: mcp-server-weixin
#  main:
#    banner-mode: off
#    web-application-type: none
  ai:
    mcp:
      server:
        name: ${spring.application.name}
        version: 1.0.0

weixin:
  api:
    original_id: gh_e067c267e056
    app-id: wxc56b69e84f0d1553
    app-secret: 513b4e58b3a31b529c84c1bd3c8d1cbc
    template_id: O8qI6gy75F-bXfPiQugInTMLA0MRzaMff9WSBb16cFk
    touser: or0Ab6ivwmypESVp_bYuk92T6SvU

logging:
  level:
    root: INFO
    cn.bugstack.mcp.server.weixin: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: data/log/${spring.application.name}.log


